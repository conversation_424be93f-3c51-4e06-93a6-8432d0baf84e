package com.paic.ncbs.claim.common.util;

import org.springframework.util.ReflectionUtils;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtils {

    private static final String TRANS_DTO_TO_STR_ERROR = "转换DTO到字符串出错";

    private static final String ID_CARD_NO_IS_NOT_RIGHT = "idCardNo is not right";

    public final static String REG_NUMBER = "^[0-9]*$";

    public final static String REG_SPECIAL_STRING = "[.。/、 -'&;；:：,*]";

    public final static String REG_IDCARD_15 = "^[1-9]\\d{13}[Xx|0-9]$";

    public final static String REG_IDCARD_18 = "^[1-9]\\d{16}[Xx|0-9]$";

    private final static String REG_MOBILE = "^1(3|4|7|5|6|8|9)([0-9]{9})$";

    private final static String REG_INTERNATIONAL_MOBILE = "^(\\+\\d+)?1[3456789]\\d{9}$";

    private final static String REG_CONTACT_TELEPHONE = "(?:(\\(\\+?86\\))(0[0-9]{2,3}\\-?)?([2-9][0-9]{6,7})+(\\-[0-9]{1,4})?)|(?:(86-?)?(0[0-9]{2,3}\\-?)?([2-9][0-9]{6,7})+(\\-[0-9]{1,4})?)";

    private final static String REG_400_TELEPHONE = "^400[0-9]{7}$";

    private final static String REG_800_TELEPHONE = "^800[0-9]{7}$";

    public static final int NORMAL_PHONE_LENGTH = 11;

    public static final String CHARACTER_FORMAT = "GBK";

    public static int GUID = 100;

    private static String ID_CARD_NO_ERROR = "idCardNo is not right";

    private static String COVERT_DTO_TO_STR_ERROR = "转换DTO到字符串出错";

    private static SecureRandom random = new SecureRandom();

    public static final char[] SEEDS = {
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
    };

    public static String getRandomStr(int length) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            stringBuilder.append(SEEDS[random.nextInt(62)]);
        }
        return stringBuilder.toString();
    }

    public static boolean isEmptyStr(Object obj) {
        if (null == obj || "".equals(obj.toString()) || "".equals(obj.toString().trim())) {
            return true;
        } else {
            return false;
        }

    }

    public static boolean isNotEmpty(String str) {
        return !isEmptyStr(str);
    }

    public static boolean isEmptyStr(String str) {
        if (null == str || "".equals(str.trim())) {
            return true;
        } else {
            return false;
        }

    }

    public static String cancelNull(String str) {
        return str == null ? "" : str;
    }

    public static String getTrimStr(String str, String defaultStr) {
        if (isEmptyStr(str) || "undefined".equals(str)) {
            str = defaultStr;
        }
        return str.trim();
    }

    public static String getTrimString(String str) {
        if (isEmptyStr(str)) {
            return "";
        }
        return str.replaceAll("[　*| *| *|//s*]*", "");

    }

    public static String getTrimPerStr(String str) {
        if (isEmptyStr(str)) {
            str = "";
        } else {
            str = str + "%";
        }
        return str.trim();
    }

    public static Date getBirthdayFromIdCard(String idCardNo) {
        if (isEmptyStr(idCardNo)
                || !(idCardNo.matches(REG_IDCARD_15) || idCardNo.matches(REG_IDCARD_18))) {
            throw new IllegalArgumentException(ID_CARD_NO_IS_NOT_RIGHT);
        }
        String birdayStr = "";
        Date birday = null;
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

        try {
            if (!isEmptyStr(idCardNo) && 15 == idCardNo.length()) {
                birdayStr = "19" + idCardNo.substring(6, 12);
                birday = dateFormat.parse(birdayStr);
            } else if (!isEmptyStr(idCardNo) && 18 == idCardNo.length()) {
                birdayStr = idCardNo.substring(6, 14);
                birday = dateFormat.parse(birdayStr);
            } else {

            }
        } catch (ParseException e) {
            LogUtil.error("转换身份证号码的生日出错", e);
        }
        return birday;
    }

    public static String getGenderFromIdCard(String idCardNo) {
        String result = "";
        int genderNo = 0;
        if (isEmptyStr(idCardNo)
                || !(idCardNo.matches(REG_IDCARD_15) || idCardNo.matches(REG_IDCARD_18))) {
            throw new IllegalArgumentException(ID_CARD_NO_IS_NOT_RIGHT);
        }
        if (15 == idCardNo.length()) {
            genderNo = Integer.parseInt(idCardNo.substring(12));
        } else if (18 == idCardNo.length()) {
            genderNo = Integer.parseInt(idCardNo.substring(14, 17));
        } else {

        }
        result = (genderNo % 2) > 0 ? "M" : "F";
        return result;
    }

    public static String change15IdCardTo18(String idCardNo) {
        if (isEmptyStr(idCardNo)
                || !(idCardNo.matches(REG_IDCARD_15) || idCardNo.matches(REG_IDCARD_18))) {
            throw new IllegalArgumentException(ID_CARD_NO_IS_NOT_RIGHT);
        }
        if (15 == idCardNo.length()) {
            final int[] W = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1};
            final String[] A = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};
            int i, j, s = 0;
            String newid;
            newid = idCardNo;
            newid = newid.substring(0, 6) + "19" + newid.substring(6, idCardNo.length());
            for (i = 0; i < newid.length(); i++) {
                j = Integer.parseInt(newid.substring(i, i + 1)) * W[i];
                s = s + j;
            }
            s = s % 11;
            newid = newid + A[s];
            return newid;
        } else {
            return idCardNo;
        }

    }

    public static boolean validTelPhone(String telPhone) {
        if (telPhone.matches(REG_INTERNATIONAL_MOBILE) || telPhone.matches(REG_CONTACT_TELEPHONE) ||
                telPhone.matches(REG_400_TELEPHONE) || telPhone.matches(REG_800_TELEPHONE)) {
            return true;
        }

        return false;
    }

    public static boolean isSamePhone(String phone1, String phone2) {
        if (phone1.length() == phone2.length() && phone1.length() == 11) {
            return phone1.equals(phone2);
        } else if (phone1.length() > 11 || phone2.length() > 11) {
            phone1 = phone1.substring(phone1.length() - 11);
            return phone2.endsWith(phone1);
        } else {
            return false;
        }
    }

    public static boolean validTelPhoneArr(String[] mobileArr) {
        for (String mobile : mobileArr) {
            if (!validTelPhone(mobile)) {
                return false;
            }
        }

        return true;
    }

    public static boolean validMobile(String mobile) {
        if (mobile.matches(REG_MOBILE)) {
            return true;
        }

        return false;
    }

    public static boolean validIdCardNoReg(String idCardNo) {
        if (idCardNo.matches(REG_IDCARD_15) || idCardNo.matches(REG_IDCARD_18)) {
            return true;
        }

        return false;
    }

    public static boolean validLetterNumber(String str) {
        String regEx = "^[A-Za-z0-9]+$";

        if (str.matches(regEx)) {
            return true;
        }

        return false;
    }
    public static boolean validLetterNumberAndSize(String str,String a, String b) {
        String regEx = "^[A-Za-z0-9]{"+a+","+b+"}$";

        if (str.matches(regEx)) {
            return true;
        }

        return false;
    }

    public static boolean validSocialCreditCodeReg(String socialCreditCode) {

        String regSocialCreditCode = "[^_IOZSVa-z\\W]{2}\\d{6}[^_IOZSVa-z\\W]{10}";
        if (socialCreditCode.matches(regSocialCreditCode)) {
            return true;
        }

        return false;
    }

    public static boolean validSBankAccountReg(String bankAccount) {

        String regBankAccount = "^([1-9]{1})([0-9]{9,19})$";
        if (bankAccount.matches(regBankAccount)) {
            return true;
        }

        return false;
    }

    public static int getAgeFromIdCard(String idCardNo) {
        Date birthday = getBirthdayFromIdCard(idCardNo);
        Calendar calBirthday = Calendar.getInstance();
        calBirthday.setTime(birthday);
        calBirthday.get(Calendar.YEAR);

        Calendar calToday = Calendar.getInstance();
        int diffYear = calToday.get(Calendar.YEAR) - calBirthday.get(Calendar.YEAR);

        calBirthday.add(Calendar.YEAR, diffYear);
        if (calBirthday.after(calToday))//如果今天的日期还没到达今年的生日，说明周岁还需要减1
        {
            diffYear = diffYear - 1;
        }
        return diffYear;
    }

    public static int getAgeFromIdCard(String idCardNo, Date limitDate) {
        if (limitDate == null) {
            return getAgeFromIdCard(idCardNo);
        }
        Date birthday = getBirthdayFromIdCard(idCardNo);
        Calendar calBirthday = Calendar.getInstance();
        calBirthday.setTime(birthday);

        Calendar calToday = Calendar.getInstance();
        calToday.setTime(limitDate);
        int diffYear = calToday.get(Calendar.YEAR) - calBirthday.get(Calendar.YEAR);

        calBirthday.add(Calendar.YEAR, diffYear);
        if (calBirthday.after(calToday))//如果今天的日期还没到达今年的生日，说明周岁还需要减1
        {
            diffYear = diffYear - 1;
        }
        return diffYear;
    }


    public static String toLowerCase(String str) {
        if (str == null) {
            return "";
        } else {
            return str.toLowerCase();
        }

    }

    public static String toUpperCase(String str) {
        if (str == null) {
            return "";
        } else {
            return str.toUpperCase();
        }
    }

    public static List<String> toUpperCaseAll(List<String> list) {
        List<String> ret = new ArrayList<String>();
        for (String str : list) {
            str = toUpperCase(str);
            ret.add(str);
        }
        return ret;
    }

    public static String trimStringArray(String str, String split) {
        StringBuffer sb = new StringBuffer();
        if (StringUtils.isEmptyStr(str)) {
            return str;
        } else {
            for (String s : str.split(split)) {
                sb.append(s.trim()).append(split);
            }
        }
        if (sb.toString().endsWith(split)) {
            sb = new StringBuffer(sb.substring(0, sb.length() - split.length()));
        }
        return sb.toString();
    }

    public static String toUpper1stLetter(String str) {
        if (str == null) {
            return "";
        } else {
            return str.substring(0, 1).toUpperCase() + str.substring(1, str.length());
        }
    }

    public static String toUpperFirstLetter(String str) {
        if (isEmptyStr(str)) {
            return str;
        }
        char[] strArr = str.toCharArray();
        if (strArr[0] < 'a' || strArr[0] > 'z') {
            return str;
        }
        strArr[0] -= 32;
        return String.valueOf(strArr);
    }

    public static String getMergeString(List<String> list, String delimeter) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        if (list.size() == 1) {
            return (String) list.get(0);
        }
        StringBuffer sb = new StringBuffer();

        Iterator<String> iter = list.iterator();
        String str;
        while (iter.hasNext()) {
            str = iter.next();
            sb.append(str);
            sb.append(delimeter);
        }
        return sb.substring(0, sb.lastIndexOf(delimeter));
    }

    public static Integer strToInt(String inValue) {
        int outValue = 0;
        try {
            outValue = Integer.parseInt(inValue);
        } catch (NumberFormatException ex) {
        } catch (NullPointerException ex) {
        }
        return Integer.valueOf(outValue);
    }

    public static boolean isEmptyStrArray(String[] arg) {
        boolean flag = false;
        if (null == arg || arg.length == 0) {
            flag = true;
        }
        return flag;
    }

    public static BigDecimal convertStringToBigDecimal(String str) {
        BigDecimal bigDecimal = null;
        try {
            bigDecimal = new BigDecimal(str);
        } catch (Exception e) {
        }
        return bigDecimal;
    }

    public static BigDecimal convertStrToBigDecimal(String str) {
        if (isEmptyStr(str)) {
            return new BigDecimal(0);
        }
        BigDecimal bigDecimal = new BigDecimal(0);
        try {
            bigDecimal = new BigDecimal(str);
        } catch (Exception e) {
        }
        return bigDecimal;
    }

    public static String emptyStrConvert(String in, String out) {
        return isNotEmpty(in) ? in : out;
    }

    public static Long strToLong(String inValue) {
        long outValue = 0;
        try {
            outValue = Long.parseLong(inValue);
        } catch (NumberFormatException ex) {
        } catch (NullPointerException ex) {
        }
        return Long.valueOf(outValue);
    }

    public static int getWordCount(String s) {
        if (isEmptyStr(s)) {
            return 0;
        }
        int length = 0;
        for (int i = 0; i < s.length(); i++) {
            int ascii = Character.codePointAt(s, i);
            if (ascii >= 0 && ascii <= 255) {
                length++;
            } else {
                length += 2;
            }

        }
        return length;

    }

    public static String subString(String s, int n, String endWith) {
        if (s == null) {
            return "";
        }
        Pattern p = Pattern.compile("^[\\u4e00-\\u9fa5]|[\\uff08]|[\\uff09]$");
        int i = 0, j = 0;
        char[] ch = s.toCharArray();
        char c;
        for (int k = 0; k < ch.length; k++) {
            c = ch[k];
            Matcher m = p.matcher(String.valueOf(c));
            i += m.find() ? 2 : 1;
            ++j;
            if (i == n) {
                break;
            }
            if (i > n) {
                --j;
                break;
            }
        }
        return s.substring(0, j) + endWith;
    }

    public static String escape(String src) {
        int i;
        char j;
        StringBuffer tmp = new StringBuffer();
        tmp.ensureCapacity(src.length() * 6);
        for (i = 0; i < src.length(); i++) {
            j = src.charAt(i);
            if (Character.isDigit(j) || Character.isLowerCase(j) || Character.isUpperCase(j)) {
                tmp.append(j);
            } else if (j < 256) {
                tmp.append("%");
                if (j < 16) {
                    tmp.append("0");
                }

                tmp.append(Integer.toString(j, 16));
            } else {
                tmp.append("%u");
                tmp.append(Integer.toString(j, 16));
            }
        }
        return tmp.toString();
    }

    public static String unescape(String src) {
        StringBuffer tmp = new StringBuffer();
        tmp.ensureCapacity(src.length());
        int lastPos = 0, pos = 0;
        char ch;
        while (lastPos < src.length()) {
            pos = src.indexOf('%', lastPos);
            if (pos == lastPos) {
                if (src.charAt(pos + 1) == 'u') {
                    ch = (char) Integer.parseInt(src.substring(pos + 2, pos + 6), 16);
                    tmp.append(ch);
                    lastPos = pos + 6;
                } else {
                    ch = (char) Integer.parseInt(src.substring(pos + 1, pos + 3), 16);
                    tmp.append(ch);
                    lastPos = pos + 3;
                }
            } else {
                if (pos == -1) {
                    tmp.append(src.substring(lastPos));
                    lastPos = src.length();
                } else {
                    tmp.append(src.substring(lastPos, pos));
                    lastPos = pos;
                }
            }
        }
        return tmp.toString();
    }

    public static boolean checkMatcher(String regStr, String matStr) {
        Pattern pattern = Pattern.compile(regStr);
        Matcher matcher = pattern.matcher(matStr);
        return matcher.matches();
    }

    public static boolean isEqualStr(String str1, String str2) {
        return cancelNull(str1).equals(cancelNull(str2));
    }

    public static boolean isHaveBAblankBankStr(String str) {
        String tmpStr = str.trim();

        return tmpStr.length() != str.length();
    }

    public static String stringTrim(String str) {
        if (isNotEmpty(str)) {
            str = str.replaceAll("\\s+", "");
            str = stringFullCornerTrim(str);
            return str;
        }
        return str;
    }

    public static String getDefaultString(String str) {
        if (isEmptyStr(str)) {
            return "";
        }
        return str;
    }

    public static String getPrimaryFieldByTableName(String tableName) {
        StringBuffer primaryKey = new StringBuffer("id");
        if (StringUtils.isNotEmpty(tableName)) {
            String lowerTableName = tableName.toLowerCase();
            String[] items = lowerTableName.split("_");
            for (String item : items) {
                primaryKey.append(item.substring(0, 1).toUpperCase()).append(item.substring(1, item.length()));
            }
        }
        return primaryKey.toString();
    }

    public static String getPrimaryKeyByTableName(String tableName) {
        StringBuffer primaryKey = new StringBuffer("id_");
        primaryKey.append(tableName);
        return primaryKey.toString();
    }

    public static String getGetMethodNameByColumn(String columnName) {
        StringBuffer getMethodName = new StringBuffer("get");
        return getMethodName.append(columnName.substring(0, 1).toUpperCase()).append(columnName.substring(1, columnName.length())).toString();
    }

    public static String getColumnNameByFiledName(String fieldName) {
        if (StringUtils.isEmptyStr(fieldName)) {
            return "";
        }
        StringBuffer col = new StringBuffer();
        for (int i = 0; i < fieldName.length(); i++) {
            char s = fieldName.charAt(i);
            int as = (int) s;
            if (as >= 65 && as <= 90) {
                col.append("_").append((char) (as + 32));
            } else {
                col.append(s);
            }
        }
        return col.toString();
    }

    public static String stringFullCornerTrim(String str) {
        if (isNotEmpty(str)) {

        }
        //String regStartSpace = "^[　 ]*"  			String regEndSpace = "[　 ]*$"    			str = str.replaceAll(regStartSpace, "").replaceAll(regEndSpace, "");
        return str;
    }

    public static String cancelNullObj(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    public static boolean isHaveOneEmpty(String... args) {
        boolean retValue = false;
        if (null != args) {
            for (int i = 0; i < args.length; i++) {
                if (isEmptyStr(args[i])) {
                    retValue = true;
                    return retValue;
                }
            }
        }
        return retValue;
    }

    public static boolean isNumStr(String str) {
        boolean ret = false;
        ret = checkMatcher("^\\d+$", str);
        return ret;
    }

    public static String removeAllBlank(String str) {
        String dest = "";
        if (str != null) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }

    public static boolean islegalLength(String str, int dfNum) {
        boolean res = false;
        String noBlankStr = removeAllBlank(str);
        try {
            int byLen = noBlankStr.getBytes("gbk").length;
            if (byLen <= dfNum) {
                res = true;
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return res;
    }

    public static String[] splitString(String str, String separator) {
        String[] results = null;
        if (isNotEmpty(str)) {
            if (str.indexOf(separator) != -1) {
                results = str.split(separator);
            } else {
                results = new String[]{str};
            }
        } else {
            results = new String[]{};
        }
        return results;
    }

    public static String upperAleph(String str) {
        char[] cs = str.toCharArray();
        if (cs[0] < 97 || cs[0] > 122) {
            return str;
        }
        cs[0] -= 32;
        return String.valueOf(cs);
    }

    public static String valueOf(Object paramObject, String defaultValue) {
        return ((paramObject == null || "".equals(((String) paramObject).trim())) ? defaultValue : paramObject.toString());
    }

    public static String changeCharset(String str, String oldCharset, String newCharset)
            throws UnsupportedEncodingException {
        if (str != null) {
            byte[] bs = str.getBytes(oldCharset);
            return new String(bs, newCharset);
        }
        return null;
    }

    public static String getStackTraceClassName(int index) {

        String className = Thread.currentThread().getStackTrace()[index].getClassName();
        String methodName = Thread.currentThread().getStackTrace()[index].getMethodName();
        if (StringUtils.isNotEmpty(className) && StringUtils.isNotEmpty(methodName)) {
            String cs = ".";
            if (className.contains(cs)) {
                className = className.substring(className.lastIndexOf(cs) + 1);
            }
            StringBuffer strb = new StringBuffer();
            strb.append(className);
            strb.append(".");
            strb.append(methodName);
            return strb.toString();
        }
        return null;
    }

    public static boolean checkFindMethodOfMatcher(String regStr, String matStr) {
        Pattern pattern = Pattern.compile(regStr);
        Matcher matcher = pattern.matcher(matStr);
        return matcher.find();
    }

    public static String getUUID() {
        String uuId = UUID.randomUUID().toString().replace("-", "");
        return uuId;
    }

    public static Map<String, String> parseMessageVariables(String variables, String separator) {
        Map<String, String> message = new HashMap<String, String>();
        if (!StringUtils.isEmptyStr(variables)) {
            for (StringTokenizer st = new StringTokenizer(variables, separator); st.hasMoreTokens(); ) {
                String key = st.nextToken();
                if (st.hasMoreTokens()) {
                    message.put(key, st.nextToken());
                }
            }
        }

        return message;
    }

    public static String getFlowTypeByCaseTimes(Integer caseTimes) {
        String flowType = "";
        if (caseTimes <= 9) {
            flowType = "0" + caseTimes;
        } else {
            flowType = String.valueOf(caseTimes);
        }
        return flowType;
    }

    public static String removeSpecialSymbolByReg(String str, String reg) {
        Pattern p = Pattern.compile(reg);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    public static List<String> getListWithSeparator(String str, String separator) {
        if (StringUtils.isEmptyStr(str)) {
            return null;
        }
        String[] arr = splitString(str, separator);

        return Arrays.asList(arr);
    }

    public static String subStrZh(String str, int subSLength) {
        String subStr = "";

        if (str == null) {
            return subStr;
        } else {
            try {

                subStr = str.substring(0, str.length() < subSLength ? str.length() : subSLength);
                int subStrByetsL = subStr.getBytes(CHARACTER_FORMAT).length;
                if (subStrByetsL < subSLength) {
                    return str;
                } else {
                    subSLength = subSLength - 1;
                }
                int tempSubLength = subSLength;
                LogUtil.audit("截取前的字符串长度为:" + subStrByetsL);

                while (subStrByetsL > tempSubLength) {
                    int subSLengthTemp = --subSLength;
                    subStr = str.substring(0, subSLengthTemp > str.length() ? str.length() : subSLengthTemp);
                    subStrByetsL = subStr.getBytes(CHARACTER_FORMAT).length;
                }

                LogUtil.audit("截取后的字符串长度为:" + subStrByetsL);
            } catch (UnsupportedEncodingException e) {
                LogUtil.audit("按字节长度截取字符串失败", e);
                subStr = "";
            }
        }
        return subStr;
    }

    public static boolean isNumeric(String[] mobileArr) {

        for (String mobile : mobileArr) {
            Pattern pattern = Pattern.compile("[0-9]*");
            Matcher isNum = pattern.matcher(mobile);
            if (!isNum.matches()) {
                return false;
            }
        }

        return true;
    }

    public static String encrypt(String clientBankAccount) {
        int length = clientBankAccount.length();
        int replaceLength = length - 8;
        StringBuilder replaceStr = new StringBuilder("");
        for (int i = 0; i < replaceLength; i++) {
            replaceStr.append("*");
        }
        StringBuilder replaceBankAccount = new StringBuilder(clientBankAccount);
        replaceBankAccount.replace(4, length - 4, replaceStr.toString());
        LogUtil.audit("replaceBankAccount 出参=" + replaceBankAccount.toString());
        return replaceBankAccount.toString();
    }


    public static String getShowPolicyNo(String policyNo, String policyCerNo) {
        String showPolicyNo = policyNo;

        if (isNotEmpty(policyCerNo)) {
            showPolicyNo = showPolicyNo + "(" + policyCerNo + ")";
        }

        return showPolicyNo;
    }

    public static boolean isContainChinese(String str) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }

    public static String StringFormatWithLine(String str, int byteLength) {
        if (isEmptyStr(str)) {
            return str;
        }
        StringBuilder sb = new StringBuilder();
        try {
            int strByteLength = str.getBytes("GBK").length;
            if (strByteLength <= byteLength) {
                return str;
            }
            char[] strArr = str.toCharArray();
            int chineseCount = 0;
            int englishCount = 0;
            for (int i = 0, arrLength = strArr.length; i < arrLength; i++) {
                int byteLen = (strArr[i] + "").getBytes("GBK").length;
                if (byteLen == 2) {
                    chineseCount++;
                } else if (byteLen == 1) {
                    englishCount++;
                } else {

                }

                int index = ((chineseCount << 1) + englishCount) % byteLength;
                if (index == 0) {
                    sb.append(strArr[i]);
                    sb.append(System.getProperty("line.separator"));
                } else if (index == 1 && byteLen == 2) {
                    sb.append(System.getProperty("line.separator"));
                    sb.append(strArr[i]);
                } else {
                    sb.append(strArr[i]);
                }
            }
        } catch (Exception e) {
            LogUtil.error("字符串换行异常", e);
        }
        return sb.toString();
    }

    public static String StringFormatWithSpace(final String str, int minByte, int maxByte) {
        if (isEmptyStr(str)) {
            return str;
        }
        StringBuilder sb = new StringBuilder();
        try {
            int strByteLength = str.getBytes("GBK").length;
            if (strByteLength < minByte || strByteLength >= maxByte) {
                return str;
            }

            int spaceCount = maxByte - strByteLength;
            for (int i = 0; i < spaceCount; i++) {
                sb.append(" ");
            }
        } catch (Exception e) {
            LogUtil.error("字符串拼接异常", e);
        }
        return str + sb.toString();
    }

    public static String getGuid() {

        GUID += 1;

        long now = System.currentTimeMillis();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
        String time = dateFormat.format(now);
        String info = now + "";
        int ran = 0;
        if (GUID > 999) {
            GUID = 100;
        }
        ran = GUID;

        return time + info.substring(2, info.length()) + ran;
    }

    public static String nvl(String str1, String str2) {
        if (isEmptyStr(str1)) {
            return str2;
        }
        return str1;
    }

    public static boolean judgeContainsStr(String cardNum) {
        String regex = ".*[a-zA-Z]+.*";
        Matcher m = Pattern.compile(regex).matcher(cardNum);
        return m.matches();
    }

    public static boolean isEvenNum(Integer num) {
        if (num % 2 == 0) {
            return true;
        }
        return false;
    }

    public static String formatTime(long ms) {

        int ss = 1000;
        int mi = ss * 60;
        int hh = mi * 60;
        int dd = hh * 24;

        long day = ms / dd;
        long hour = (ms - day * dd) / hh;
        long minute = (ms - day * dd - hour * hh) / mi;
        long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        long milliSecond = ms - day * dd - hour * hh - minute * mi - second * ss;

        String strMinute = minute < 10 ? "0" + minute : "" + minute;
        String strSecond = second < 10 ? "0" + second : "" + second;
        String strMilliSecond = milliSecond < 10 ? "0" + milliSecond : "" + milliSecond;
        strMilliSecond = milliSecond < 100 ? "0" + strMilliSecond : "" + strMilliSecond;

        return strMinute + " 分钟 " + strSecond + " 秒";
    }

    public static List<String> getSmsPhoneList(List<String> phoneList) {
        Set<String> phones = new HashSet<String>(phoneList);
        phoneList.clear();
        phoneList.addAll(phones);
        Iterator<String> it = phoneList.iterator();
        while (it.hasNext()) {
            String phoneNum = it.next();
            if (StringUtils.isEmptyStr(phoneNum) || !StringUtils.validMobile(phoneNum)) {
                it.remove();
            }
        }
        LogUtil.audit("#获取发送短信的电话号码list, 电话list长度=%s#", phoneList.size());
        return phoneList;
    }

    public static String objectToUrlParamString(Object obj) throws IllegalAccessException {
        StringBuilder sb = new StringBuilder();
        String urlParam = "";

        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            ReflectionUtils.makeAccessible(field);
            String fieldName = field.getName();

            Object value = field.get(obj);
            if (value != null) {
                sb.append(fieldName).append("=").append(value).append("&");
            }
        }

        int len = sb.length();
        if (sb.length() > 0) {
            urlParam = sb.toString().substring(0, len - 1);
        }

        return urlParam;
    }

    public static boolean isSameChars(String str) {
        try {
            if (str.length() < 2) {
                return true;
            } else {
                char first = str.charAt(0);
                for (int i = 1; i < str.length(); i++) {
                    if (str.charAt(i) != first) {
                        return false;
                    }
                }
            }
        } catch (IllegalArgumentException e) {
            LogUtil.error("判断字符串的每一位都是相同的异常", e);
        }

        return true;
    }

    public static boolean isContinuousNumberChars(String str) {
        try {
            if (str.length() < 2) {
                return true;
            } else {
                for (int i = 0; i < str.length() - 1; i++) {
                    int startInt = Integer.parseInt(String.valueOf(str.charAt(i)));
                    int endInt = Integer.parseInt(String.valueOf(str.charAt(i + 1)));
                    if (endInt - startInt != 1) {
                        return false;
                    }
                }
            }
        } catch (IllegalArgumentException e) {
            LogUtil.error("判断字符串的是否为连续数字异常", e);
        }

        return true;
    }

    public static boolean isSpecialString(String str) throws IllegalArgumentException {
        Pattern p = Pattern.compile(REG_SPECIAL_STRING);
        Matcher m = p.matcher(str);
        return m.find();
    }

    public static String toUpperCaseFirst(String str) {
        if (StringUtils.isNotEmpty(str)) {
            char[] ch = str.toCharArray();
            if (ch[0] >= 'a' && ch[0] <= 'z') {
                ch[0] = (char) (ch[0] - 32);
            }
            str = new String(ch);
        }
        return str;
    }

    public static String toLowerCaseFirst(String str) {
        if (StringUtils.isNotEmpty(str)) {
            char[] ch = str.toCharArray();
            if (ch[0] >= 'A' && ch[0] <= 'Z') {
                ch[0] = (char) (ch[0] + 32);
            }
            str = new String(ch);
        }
        return str;
    }

    public static String getFourRandom() {
        Random random = new SecureRandom();
        String fourRandom = "" + random.nextInt(10000);
        int randLength = fourRandom.length();
        if (randLength < 4) {
            for (int i = 1; i <= 4 - randLength; i++) {
                fourRandom = "0" + fourRandom;
            }
        }
        return fourRandom;
    }

    public static String difference(String str1, String str2) {
        if (str1 == null) {
            return str2;
        } else if (str2 == null) {
            return str1;
        } else {
            int at = indexOfDifference(str1, str2);
            return at == -1 ? "" : str2.substring(at);
        }
    }

    public static int indexOfDifference(String str1, String str2) {
        if (str1 == str2) {
            return -1;
        } else if (str1 != null && str2 != null) {
            int i;
            for (i = 0; i < str1.length() && i < str2.length() && str1.charAt(i) == str2.charAt(i); ++i) {
            }

            return i >= str2.length() && i >= str1.length() ? -1 : i;
        } else {
            return 0;
        }
    }

    public static boolean isZhongTaiCase(String reportNo) {
        boolean isZhongTaiCase = false;
        if (StringUtils.isEmptyStr(reportNo)) {
            return isZhongTaiCase;
        }

        String index0 = reportNo.substring(0, 1);
        if ("8".equals(index0)) {
            isZhongTaiCase = true;
        }
        return isZhongTaiCase;
    }
}
