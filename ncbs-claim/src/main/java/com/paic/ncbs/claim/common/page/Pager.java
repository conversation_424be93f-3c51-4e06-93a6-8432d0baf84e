package com.paic.ncbs.claim.common.page;

import com.github.pagehelper.Page;

public class Pager implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private final int DEFAULT_PAGE_ROWS = 20;

    private int totalRows = 0;

    private int pageRows = DEFAULT_PAGE_ROWS;

    private int pageIndex = 1;

    public Pager() {
    }

    public Pager(int pageIndex) {
        setPageIndex(pageIndex);
    }

    public Pager(int pageIndex, int pageRows) {
        setPageIndex(pageIndex);
        setPageRows(pageRows);
    }

    public Pager(int pageIndex, int pageRows, int totalRows) {
        setPageIndex(pageIndex);
        setPageRows(pageRows);
        setTotalRows(totalRows);
    }

    public Pager(Page<?> page) {
        setPageIndex(page.getPageNum());
        setPageRows(page.getPageSize());
        setTotalRows(Integer.parseInt(String.valueOf(page.getTotal())));
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex < 1 ? 1 : pageIndex;
    }

    public void setPageRows(int pageRows) {
        this.pageRows = pageRows < 1 ? DEFAULT_PAGE_ROWS : pageRows;
    }

    public void setTotalRows(int totalRows) {
        this.totalRows = totalRows < 0 ? 0 : totalRows;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public int getPageRows() {
        return pageRows;
    }

    public int getDefaultPageRows() {
        return DEFAULT_PAGE_ROWS;
    }

    public int getTotalRows() {
        return totalRows;
    }

    public int getTotalPages() {
        return totalRows / pageRows + (totalRows % pageRows == 0 ? 0 : 1);
    }

    public int getCurrPageRows() {
        if (pageIndex > getTotalPages()) {
            return 0;
        } else if (totalRows / pageIndex < pageRows) {
            return totalRows % pageRows;
        } else {
            return pageRows;
        }
    }

    public boolean getHasPrevPage() {
        return pageIndex > 1;
    }

    public boolean getHasNextPage() {
        return pageIndex < getTotalPages();
    }

    @Override
    public String toString() {
        return "{pageIndex:" + getPageIndex() + ",pageRows:" + getPageRows() + ",totalRows:"
                + getTotalRows() + ",totalPages:" + getTotalPages() + ",currPageRows:"
                + getCurrPageRows() + ",pageStartRow:" + ",pageEndRow:"
                + ",hasPrevPage:" + getHasPrevPage() + ",hasNextPage:"
                + getHasNextPage() + "}";
    }

}