package com.paic.ncbs.claim.controller.indicator;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.indicator.CaseIndicatorDTO;
import com.paic.ncbs.claim.service.indicator.CaseIndicatorService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;

/**
 * @author: justinwu
 * @create 2025/3/7 10:09
 */
@RestController
@RequestMapping("/public/indicator")
@Api(tags = {"案件指标"})
@Slf4j
public class CaseIndicatorController {
    @Autowired
    private CaseIndicatorService caseIndicatorService;
    @GetMapping("/settleDeadlineForUnsettle")
    public ResponseResult settleDeadlineForUnsettle(CaseIndicatorDTO caseIndicatorDTO) throws GlobalBusinessException {
        try {
            caseIndicatorService.settleDeadlineForUnsettle(caseIndicatorDTO);
        } catch (ParseException e) {
            throw new GlobalBusinessException("settleDeadlineForUnsettle计算首次结案时效");
        }
        return ResponseResult.success();
    }

    @GetMapping("/settleDeadlineForSettle")
    public ResponseResult settleDeadlineForSettle(CaseIndicatorDTO caseIndicatorDTO) throws GlobalBusinessException {
        try {
            caseIndicatorService.settleDeadlineForSettle(caseIndicatorDTO);
        } catch (ParseException e) {
            throw new GlobalBusinessException("settleDeadlineForSettle计算首次结案时效");
        }
        return ResponseResult.success();
    }

    @GetMapping("/regDeadlineForUnReg")
    public ResponseResult regDeadlineForUnReg(CaseIndicatorDTO caseIndicatorDTO) throws GlobalBusinessException {
        try {
            caseIndicatorService.regDeadlineForUnReg(caseIndicatorDTO);
        } catch (ParseException e) {
            throw new GlobalBusinessException("regDeadlineForUnReg计算立案时效");
        }
        return ResponseResult.success();
    }

    @GetMapping("/regDeadlineForReg")
    public ResponseResult regDeadlineForReg(CaseIndicatorDTO caseIndicatorDTO) throws GlobalBusinessException {
        try {
            caseIndicatorService.regDeadlineForReg(caseIndicatorDTO);
        } catch (ParseException e) {
            throw new GlobalBusinessException("regDeadlineForReg计算立案时效");
        }
        return ResponseResult.success();
    }

    @GetMapping("/reopenDeadline")
    public ResponseResult reopenDeadline(CaseIndicatorDTO caseIndicatorDTO) throws GlobalBusinessException {
        try {
            caseIndicatorService.reopenDeadline(caseIndicatorDTO);
        } catch (ParseException e) {
            throw new GlobalBusinessException("reopenDeadline计算重开结案时效");
        }
        return ResponseResult.success();
    }
}
